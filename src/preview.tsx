import ReactDOM, { findDOMNode } from 'react-dom';
import React, { useState } from 'react';
import mergeWith from 'lodash/mergeWith';
import isArray from 'lodash/isArray';
import { buildComponents, assetBundle, AssetLevel, AssetLoader } from '@alilc/lowcode-utils';
import ReactRenderer from '@alilc/lowcode-react-renderer';
import { injectComponents } from '@alilc/lowcode-plugin-inject';
import appHelper from './appHelper';
import { getUrlSearchParams } from './services/utils';
import './preview.scss';
import {
  getProjectSchemaFromLocalStorage,
  getPackagesFromLocalStorage,
  getPreviewLocale,
  setPreviewLocale,
  getPageState,
} from './services/pageService';
import './utils/requestConfig';
import './utils/ta';
import { addCheckDom, intList } from './utils/docAddonEdit';
import { dictInit } from './services/dict';

const getScenarioName = function () {
  if (location.search) {
    return new URLSearchParams(location.search.slice(1)).get('scenarioName') || 'general';
  }
  return 'general';
};

const SamplePreview = () => {
  const [data, setData] = useState({});

  async function init() {
    await dictInit();

    // 跳过serviceInit调用，直接使用编辑态数据进行预览
    // 这样可以避免SSO校验，直接使用保存在localStorage中的编辑态数据

    const scenarioName = getScenarioName();
    const packages = await getPackagesFromLocalStorage(scenarioName);
    const projectSchema = getProjectSchemaFromLocalStorage(scenarioName);
    const {
      componentsMap: componentsMapArray,
      componentsTree,
      i18n,
      dataSource: projectDataSource,
    } = projectSchema;
    const componentsMap: any = {};
    componentsMapArray.forEach((component: any) => {
      componentsMap[component.componentName] = component;
    });
    const pageSchema = componentsTree[0];

    const libraryMap: any = {};
    const libraryAsset: any[] = [];
    packages.forEach((pkg: any) => {
      const { package: _package, library, urls, renderUrls } = pkg;
      libraryMap[_package] = library;
      if (renderUrls) {
        libraryAsset.push(renderUrls);
      } else if (urls) {
        libraryAsset.push(urls);
      }
    });

    const vendors = [assetBundle(libraryAsset, AssetLevel.Library)];

    // TODO asset may cause pollution
    const assetLoader = new AssetLoader();
    await assetLoader.load(libraryAsset);
    const components = await injectComponents(buildComponents(libraryMap, componentsMap));

    setData({
      schema: pageSchema,
      pageConfig: {}, // 设置默认的空配置，避免SSO校验
      components,
      i18n,
      projectDataSource,
    });

    // 设置moment
    window.moment.locale('zh-cn', {
      months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),
      monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),
      weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),
      weekdaysShort: '周日_周一_周二_周三_周四_周五_周六'.split('_'),
      weekdaysMin: '日_一_二_三_四_五_六'.split('_'),
      longDateFormat: {
        LT: 'HH:mm',
        LTS: 'HH:mm:ss',
        L: 'YYYY/MM/DD',
        LL: 'YYYY年M月D日',
        LLL: 'YYYY年M月D日Ah点mm分',
        LLLL: 'YYYY年M月D日ddddAh点mm分',
        l: 'YYYY/M/D',
        ll: 'YYYY年M月D日',
        lll: 'YYYY年M月D日 HH:mm',
        llll: 'YYYY年M月D日dddd HH:mm',
      },
      meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,
      meridiemHour: function (hour, meridiem) {
        if (hour === 12) {
          hour = 0;
        }
        if (meridiem === '凌晨' || meridiem === '早上' || meridiem === '上午') {
          return hour;
        } else if (meridiem === '下午' || meridiem === '晚上') {
          return hour + 12;
        } else {
          // '中午'
          return hour >= 11 ? hour : hour + 12;
        }
      },
      meridiem: function (hour, minute, isLower) {
        var hm = hour * 100 + minute;
        if (hm < 600) {
          return '凌晨';
        } else if (hm < 900) {
          return '早上';
        } else if (hm < 1130) {
          return '上午';
        } else if (hm < 1230) {
          return '中午';
        } else if (hm < 1800) {
          return '下午';
        } else {
          return '晚上';
        }
      },
      calendar: {
        sameDay: '[今天]LT',
        nextDay: '[明天]LT',
        nextWeek: function (now) {
          if (now.week() !== this.week()) {
            return '[下]dddLT';
          } else {
            return '[本]dddLT';
          }
        },
        lastDay: '[昨天]LT',
        lastWeek: function (now) {
          if (this.week() !== now.week()) {
            return '[上]dddLT';
          } else {
            return '[本]dddLT';
          }
        },
        sameElse: 'L',
      },
      dayOfMonthOrdinalParse: /\d{1,2}(日|月|周)/,
      ordinal: function (number, period) {
        switch (period) {
          case 'd':
          case 'D':
          case 'DDD':
            return number + '日';
          case 'M':
            return number + '月';
          case 'w':
          case 'W':
            return number + '周';
          default:
            return number;
        }
      },
      relativeTime: {
        future: '%s后',
        past: '%s前',
        s: '几秒',
        ss: '%d 秒',
        m: '1 分钟',
        mm: '%d 分钟',
        h: '1 小时',
        hh: '%d 小时',
        d: '1 天',
        dd: '%d 天',
        w: '1 周',
        ww: '%d 周',
        M: '1 个月',
        MM: '%d 个月',
        y: '1 年',
        yy: '%d 年',
      },
      week: {
        // GB/T 7408-1994《数据元和交换格式·信息交换·日期和时间表示法》与ISO 8601:1988等效
        dow: 1, // Monday is the first day of the week.
        doy: 4, // The week that contains Jan 4th is the first week of the year.
      },
    });
  }

  const { schema, components, i18n = {}, projectDataSource = {} } = data as any;

  console.log('[ 🚀 Preview Data ]', data);

  if (!schema || !components) {
    init();
    return (
      <div
        style={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <img
          src="https://hex.melgeek.cn/web/image/website/1/favicon?unique=f4c053b"
          style={{ width: '60px' }}
        />
      </div>
    );
  }

  // 移除SSO验证相关逻辑，直接进行预览渲染

  const currentLocale = getPreviewLocale(getScenarioName());

  if (!(window as any).setPreviewLocale) {
    // for demo use only, can use this in console to switch language for i18n test
    // 在控制台 window.setPreviewLocale('en-US') 或 window.setPreviewLocale('zh-CN') 查看切换效果
    (window as any).setPreviewLocale = (locale: string) =>
      setPreviewLocale(getScenarioName(), locale);
  }

  function customizer(objValue: [], srcValue: []) {
    if (isArray(objValue)) {
      return objValue.concat(srcValue || []);
    }
  }

  const anchorItems =
    (schema?.children || []).find((item: any) => item && item.componentName === 'JNPageAnchor')
      ?.props.anchorItems || [];
  const pageFirstLevelNode = schema?.children || [];
  // 根据页面url来判断是否为云组件的编辑状态标识为docAddonEdit
  const docAddonEdit = getUrlSearchParams('docAddonEdit');
  const modules = getUrlSearchParams('baseModules');
  // 检查是否有选中的modules, 如果有则需要初始化list
  if (modules) {
    console.log('modules-------------------', modules);
    intList(modules.split(','));
  }
  return (
    <div className="lowcode-plugin-sample-preview" style={{ background: '#f1f1f1' }}>
      <ReactRenderer
        className="lowcode-plugin-sample-preview-content"
        schema={{
          ...schema,
          dataSource: mergeWith(schema.dataSource, projectDataSource, customizer),
        }}
        components={components}
        onCompGetRef={(schema, ref) => {
          const domNode = findDOMNode(ref); // ref: componentInstance 类式组件
          const isAnchor = anchorItems.length && anchorItems.find((item) => item.id === schema?.id);
          if (domNode && domNode instanceof HTMLElement && schema?.id && isAnchor) {
            domNode.setAttribute('id', schema.id);
          }
          // 根据页面url来判断是否为云组件的编辑状态标识为docAddonEdit
          // 判断是否第一级

          if (docAddonEdit) {
            const findItem = pageFirstLevelNode.find((item: any) => item.id === schema.id);
            if (findItem) {
              const domNode = findDOMNode(ref) as Element; // ref: componentInstance 类式组件
              addCheckDom(domNode, schema);
            }
          }
        }}
        locale={currentLocale}
        messages={i18n}
        appHelper={appHelper}
      />
    </div>
  );
};
ReactDOM.render(<SamplePreview />, document.getElementById('ice-container'));
